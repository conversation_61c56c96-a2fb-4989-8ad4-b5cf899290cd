#include <mutex>
#include <vector>
#include <queue>
#include <memory>
#include <iostream>
#include <chrono>
#include <rclcpp/rclcpp.hpp>
#include <sensor_msgs/msg/imu.hpp>
#include <livox_ros_driver2/msg/custom_msg.hpp>
#include <cmath>
#include "utils.h"
#include "map_builder/commons.h"
#include "map_builder/map_builder.h"

#include <pcl_conversions/pcl_conversions.h>
#include "tf2_ros/transform_broadcaster.h"
#include <geometry_msgs/msg/transform_stamped.hpp>
#include <nav_msgs/msg/path.hpp>
#include <nav_msgs/msg/odometry.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>
#include <yaml-cpp/yaml.h>
#include <sensor_msgs/msg/laser_scan.hpp>

using namespace std::chrono_literals;

struct NodeConfig {
    std::string imu_topic = "/livox/imu";
    std::string lidar_topic = "/livox/lidar";
    std::string body_frame = "body";
    std::string world_frame = "map";
    bool print_time_cost = false;
};

struct StateData {
    bool lidar_pushed = false;
    std::mutex imu_mutex;
    std::mutex lidar_mutex;
    double last_lidar_time = -1.0;
    double last_imu_time = -1.0;
    std::deque<IMUData> imu_buffer;
    std::deque<std::pair<double, pcl::PointCloud<pcl::PointXYZINormal>::Ptr>> lidar_buffer;
    nav_msgs::msg::Path path;
};

class LIONode : public rclcpp::Node {
public:
    LIONode() : Node("lio_node") {
        RCLCPP_INFO(this->get_logger(), "LIO Node Started");
        loadParameters();

        // 从配置文件读取初始位姿偏移参数
        // 这些参数用于处理odom和map坐标系的初始不对齐问题
        m_initial_pose_offset = Eigen::Matrix4d::Identity();

        // 可以通过配置文件设置初始位姿偏移，如果没有配置则使用默认值
        // 这里暂时使用之前的硬编码值，但建议移到配置文件中
        m_odom_rotation = Eigen::AngleAxisd(M_PI/4, Eigen::Vector3d::UnitZ()); // Z轴逆时针45度
        m_map_rotation = Eigen::AngleAxisd(-3*M_PI/4, Eigen::Vector3d::UnitZ()); // Z轴顺时针135度
        m_lidar_flip = Eigen::AngleAxisd(M_PI, Eigen::Vector3d::UnitX()); // X轴180度(雷达倒置)

        m_imu_sub = this->create_subscription<sensor_msgs::msg::Imu>(
            m_node_config.imu_topic, rclcpp::QoS(10).best_effort(), 
            std::bind(&LIONode::imuCB, this, std::placeholders::_1));
        
        m_lidar_sub = this->create_subscription<livox_ros_driver2::msg::CustomMsg>(
            m_node_config.lidar_topic, 10, 
            std::bind(&LIONode::lidarCB, this, std::placeholders::_1));

        m_body_cloud_pub = this->create_publisher<sensor_msgs::msg::PointCloud2>("body_cloud", 10000);
        m_world_cloud_pub = this->create_publisher<sensor_msgs::msg::PointCloud2>("world_cloud", 10000);
        m_path_pub = this->create_publisher<nav_msgs::msg::Path>("path", 10);
        m_odom_pub = this->create_publisher<nav_msgs::msg::Odometry>("odom", rclcpp::QoS(10).best_effort());
        m_ground_cloud_pub = this->create_publisher<sensor_msgs::msg::PointCloud2>("ground_cloud", 10);
        m_non_ground_cloud_pub = this->create_publisher<sensor_msgs::msg::PointCloud2>("non_ground_cloud", 10);
        m_cluster_clouds_pub = this->create_publisher<sensor_msgs::msg::PointCloud2>("cluster_clouds", 10);
        m_scan_pub = this->create_publisher<sensor_msgs::msg::LaserScan>("/scan", 10);

        m_tf_broadcaster = std::make_shared<tf2_ros::TransformBroadcaster>(*this);
        m_state_data.path.poses.clear();
        m_state_data.path.header.frame_id = m_node_config.world_frame;

        m_kf = std::make_shared<IESKF>();
        m_builder = std::make_shared<MapBuilder>(m_builder_config, m_kf);
        m_timer = this->create_wall_timer(50ms, std::bind(&LIONode::timerCB, this));
    }

    void loadParameters() {
        this->declare_parameter("config_path", "");
        std::string config_path;
        this->get_parameter("config_path", config_path);

        YAML::Node config = YAML::LoadFile(config_path);
        if (!config) {
            RCLCPP_WARN(this->get_logger(), "FAIL TO LOAD YAML FILE!");
            return;
        }

        RCLCPP_INFO(this->get_logger(), "LOAD FROM YAML CONFIG PATH: %s", config_path.c_str());

        m_node_config.imu_topic = config["imu_topic"].as<std::string>();
        m_node_config.lidar_topic = config["lidar_topic"].as<std::string>();
        m_node_config.body_frame = config["body_frame"].as<std::string>();
        m_node_config.world_frame = config["world_frame"].as<std::string>();
        m_node_config.print_time_cost = config["print_time_cost"].as<bool>();

        m_builder_config.lidar_filter_num = config["lidar_filter_num"].as<int>();
        m_builder_config.lidar_min_range = config["lidar_min_range"].as<double>();
        m_builder_config.lidar_max_range = config["lidar_max_range"].as<double>();
        m_builder_config.scan_resolution = config["scan_resolution"].as<double>();
        m_builder_config.map_resolution = config["map_resolution"].as<double>();
        m_builder_config.cube_len = config["cube_len"].as<double>();
        m_builder_config.det_range = config["det_range"].as<double>();
        m_builder_config.move_thresh = config["move_thresh"].as<double>();
        m_builder_config.na = config["na"].as<double>();
        m_builder_config.ng = config["ng"].as<double>();
        m_builder_config.nba = config["nba"].as<double>();
        m_builder_config.nbg = config["nbg"].as<double>();

        // 点云分割参数
        m_builder_config.point_cloud_segmentation_enable = config["point_cloud_segmentation_enable"].as<bool>();
        m_builder_config.voxel_size = config["voxel_size"].as<double>();
        m_builder_config.plane_distance_threshold = config["plane_distance_threshold"].as<double>();
        m_builder_config.max_iterations = config["max_iterations"].as<int>();
        m_builder_config.cluster_tolerance = config["cluster_tolerance"].as<double>();
        m_builder_config.min_cluster_size = config["min_cluster_size"].as<int>();
        m_builder_config.max_cluster_size = config["max_cluster_size"].as<int>();
        m_builder_config.ground_height_threshold = config["ground_height_threshold"].as<double>();

        m_builder_config.imu_init_num = config["imu_init_num"].as<int>();
        m_builder_config.near_search_num = config["near_search_num"].as<int>();
        m_builder_config.ieskf_max_iter = config["ieskf_max_iter"].as<int>();
        m_builder_config.gravity_align = config["gravity_align"].as<bool>();
        m_builder_config.esti_il = config["esti_il"].as<bool>();
        
        std::vector<double> t_il_vec = config["t_il"].as<std::vector<double>>();
        std::vector<double> r_il_vec = config["r_il"].as<std::vector<double>>();
        m_builder_config.t_il << t_il_vec[0], t_il_vec[1], t_il_vec[2];
        m_builder_config.r_il << r_il_vec[0], r_il_vec[1], r_il_vec[2], 
                               r_il_vec[3], r_il_vec[4], r_il_vec[5],
                               r_il_vec[6], r_il_vec[7], r_il_vec[8];
        m_builder_config.lidar_cov_inv = config["lidar_cov_inv"].as<double>();
    }

    void imuCB(const sensor_msgs::msg::Imu::SharedPtr msg) {
        std::lock_guard<std::mutex> lock(m_state_data.imu_mutex);
        double timestamp = Utils::getSec(msg->header);
        if (timestamp < m_state_data.last_imu_time) {
            RCLCPP_WARN(this->get_logger(), "IMU Message is out of order");
            m_state_data.imu_buffer.clear();
        }
        m_state_data.imu_buffer.emplace_back(
            V3D(msg->linear_acceleration.x, msg->linear_acceleration.y, msg->linear_acceleration.z) * 10.0,
            V3D(msg->angular_velocity.x, msg->angular_velocity.y, msg->angular_velocity.z),
            timestamp);
        m_state_data.last_imu_time = timestamp;
    }

    void lidarCB(const livox_ros_driver2::msg::CustomMsg::SharedPtr msg) {
        CloudType::Ptr cloud = Utils::livox2PCL(msg, 
            m_builder_config.lidar_filter_num, 
            m_builder_config.lidar_min_range, 
            m_builder_config.lidar_max_range);
        
        std::lock_guard<std::mutex> lock(m_state_data.lidar_mutex);
        double timestamp = Utils::getSec(msg->header);
        if (timestamp < m_state_data.last_lidar_time) {
            RCLCPP_WARN(this->get_logger(), "Lidar Message is out of order");
            m_state_data.lidar_buffer.clear();
        }
        m_state_data.lidar_buffer.emplace_back(timestamp, cloud);
        m_state_data.last_lidar_time = timestamp;
    }

    bool syncPackage() {
        if (m_state_data.imu_buffer.empty() || m_state_data.lidar_buffer.empty())
            return false;

        if (!m_state_data.lidar_pushed) {
            m_package.cloud = m_state_data.lidar_buffer.front().second;
            std::sort(m_package.cloud->points.begin(), m_package.cloud->points.end(),
                     [](PointType &p1, PointType &p2) { return p1.curvature < p2.curvature; });
            
            m_package.cloud_start_time = m_state_data.lidar_buffer.front().first;
            m_package.cloud_end_time = m_package.cloud_start_time + m_package.cloud->points.back().curvature / 1000.0;
            m_state_data.lidar_pushed = true;
        }

        if (m_state_data.last_imu_time < m_package.cloud_end_time)
            return false;

        m_package.imus.clear();
        while (!m_state_data.imu_buffer.empty() && 
               m_state_data.imu_buffer.front().time < m_package.cloud_end_time) {
            m_package.imus.emplace_back(m_state_data.imu_buffer.front());
            m_state_data.imu_buffer.pop_front();
        }

        m_state_data.lidar_buffer.pop_front();
        m_state_data.lidar_pushed = false;
        return true;
    }

    void publishCloud(rclcpp::Publisher<sensor_msgs::msg::PointCloud2>::SharedPtr pub,
                     CloudType::Ptr cloud, std::string frame_id, const double &time) {
        if (pub->get_subscription_count() <= 0) return;
        
        sensor_msgs::msg::PointCloud2 cloud_msg;
        pcl::toROSMsg(*cloud, cloud_msg);
        cloud_msg.header.frame_id = frame_id;
        cloud_msg.header.stamp = Utils::getTime(time);
        pub->publish(cloud_msg);
    }

    void publishOdometry(rclcpp::Publisher<nav_msgs::msg::Odometry>::SharedPtr odom_pub,
                        std::string frame_id, std::string child_frame, const double &time) {
        if (odom_pub->get_subscription_count() <= 0) return;
        
        nav_msgs::msg::Odometry odom;
        odom.header.frame_id = frame_id;
        odom.header.stamp = Utils::getTime(time);
        odom.child_frame_id = child_frame;
        
        // 原始位姿
        V3D t = m_kf->x().t_wi;
        Eigen::Quaterniond q_original(m_kf->x().r_wi);

        // 应用odom坐标系旋转修正 (Z轴逆时针45度)
        Eigen::Quaterniond q_corrected = m_odom_rotation * q_original;

        odom.pose.pose.position.x = t.x();
        odom.pose.pose.position.y = t.y();
        odom.pose.pose.position.z = t.z();
        
        odom.pose.pose.orientation.x = q_corrected.x();
        odom.pose.pose.orientation.y = q_corrected.y();
        odom.pose.pose.orientation.z = q_corrected.z();
        odom.pose.pose.orientation.w = q_corrected.w();

        V3D vel = m_kf->x().r_wi.transpose() * m_kf->x().v;
        odom.twist.twist.linear.x = vel.x();
        odom.twist.twist.linear.y = vel.y();
        odom.twist.twist.linear.z = vel.z();
        
        odom_pub->publish(odom);
    }

    void publishPath(rclcpp::Publisher<nav_msgs::msg::Path>::SharedPtr path_pub,
                    std::string frame_id, const double &time) {
        if (path_pub->get_subscription_count() <= 0) return;
        
        geometry_msgs::msg::PoseStamped pose;
        pose.header.frame_id = frame_id;
        pose.header.stamp = Utils::getTime(time);
        
        // 原始位姿
        V3D t = m_kf->x().t_wi;
        Eigen::Quaterniond q_original(m_kf->x().r_wi);

        // 应用map坐标系旋转修正 (Z轴顺时针135度)
        Eigen::Quaterniond q_corrected = m_map_rotation * q_original;

        pose.pose.position.x = t.x();
        pose.pose.position.y = t.y();
        pose.pose.position.z = t.z();
        
        pose.pose.orientation.x = q_corrected.x();
        pose.pose.orientation.y = q_corrected.y();
        pose.pose.orientation.z = q_corrected.z();
        pose.pose.orientation.w = q_corrected.w();
        
        m_state_data.path.poses.push_back(pose);
        path_pub->publish(m_state_data.path);
    }

    void broadCastTF(std::shared_ptr<tf2_ros::TransformBroadcaster> broad_caster,
                   std::string frame_id, std::string child_frame, const double &time) {
        geometry_msgs::msg::TransformStamped transformStamped;
        transformStamped.header.frame_id = frame_id;
        transformStamped.child_frame_id = child_frame;
        transformStamped.header.stamp = Utils::getTime(time);
        
        // 原始位姿
        V3D t = m_kf->x().t_wi;
        Eigen::Quaterniond q_original(m_kf->x().r_wi);

        // 选项1：使用原始位姿（推荐用于调试）
        Eigen::Quaterniond q_corrected = q_original;

        // 选项2：如果需要旋转修正，取消下面的注释
        // Eigen::Quaterniond q_corrected = m_map_rotation * m_lidar_flip * q_original;

        transformStamped.transform.translation.x = t.x();
        transformStamped.transform.translation.y = t.y();
        transformStamped.transform.translation.z = t.z();
        
        transformStamped.transform.rotation.x = q_corrected.x();
        transformStamped.transform.rotation.y = q_corrected.y();
        transformStamped.transform.rotation.z = q_corrected.z();
        transformStamped.transform.rotation.w = q_corrected.w();

        broad_caster->sendTransform(transformStamped);
    }

    void publishLaserScan(const CloudType::Ptr& cloud, const double& time) {
        sensor_msgs::msg::LaserScan scan_msg;
        scan_msg.header.stamp = Utils::getTime(time);
        scan_msg.header.frame_id = "odom";
        scan_msg.angle_min = -M_PI;
        scan_msg.angle_max = M_PI;
        scan_msg.angle_increment = M_PI / 180.0; // 1 degree resolution
        scan_msg.range_min = 0.1;
        scan_msg.range_max = 100.0;
        scan_msg.ranges.resize(360); // Assuming 360 degrees

        for (size_t i = 0; i < cloud->points.size(); ++i) {
            // Convert point cloud to LaserScan ranges
            // Assuming cloud->points[i] is of type PointType with x, y, z
            double angle = atan2(cloud->points[i].y, cloud->points[i].x);
            int index = static_cast<int>((angle - scan_msg.angle_min) / scan_msg.angle_increment);
            if (index >= 0 && index < 360) {
                double range = sqrt(cloud->points[i].x * cloud->points[i].x + cloud->points[i].y * cloud->points[i].y);
                scan_msg.ranges[index] = range;
            }
        }

        m_scan_pub->publish(scan_msg);
    }

    void timerCB() {
        if (!syncPackage()) return;
        
        auto t1 = std::chrono::high_resolution_clock::now();
        m_builder->process(m_package);
        auto t2 = std::chrono::high_resolution_clock::now();

        if (m_node_config.print_time_cost) {
            auto time_used = std::chrono::duration_cast<std::chrono::duration<double>>(t2 - t1).count() * 1000;
            RCLCPP_WARN(this->get_logger(), "Time cost: %.2f ms", time_used);
        }

        if (m_builder->status() != BuilderStatus::MAPPING)
            return;

        // 发布带有旋转修正的TF
        broadCastTF(m_tf_broadcaster, m_node_config.world_frame, m_node_config.body_frame, m_package.cloud_end_time);

        // 发布其他数据
        publishOdometry(m_odom_pub, m_node_config.world_frame, m_node_config.body_frame, m_package.cloud_end_time);

        // 方案1：发布原始的body_cloud和world_cloud（推荐）
        // body_cloud: lidar -> body坐标系变换
        CloudType::Ptr body_cloud = m_builder->lidar_processor()->transformCloud(
            m_package.cloud, m_kf->x().r_il, m_kf->x().t_il);
        publishCloud(m_body_cloud_pub, body_cloud, m_node_config.body_frame, m_package.cloud_end_time);

        // world_cloud: lidar -> world坐标系变换
        CloudType::Ptr world_cloud = m_builder->lidar_processor()->transformCloud(
            m_package.cloud, m_builder->lidar_processor()->r_wl(), m_builder->lidar_processor()->t_wl());
        publishCloud(m_world_cloud_pub, world_cloud, m_node_config.world_frame, m_package.cloud_end_time);

        // 注意：如果odom和map坐标系初始不重合，问题在于TF变换中的额外旋转修正
        // 建议移除或调整broadCastTF中的旋转修正，或者在RViz中正确设置Fixed Frame

        publishPath(m_path_pub, m_node_config.world_frame, m_package.cloud_end_time);

        // 发布点云分割结果
        if (auto point_cloud_proc = m_builder->point_cloud_processor()) {
            // 发布地面点云
            if (m_ground_cloud_pub->get_subscription_count() > 0) {
                auto ground_cloud = point_cloud_proc->getGroundCloud();
                if (ground_cloud && !ground_cloud->empty()) {
                    publishCloud(m_ground_cloud_pub, ground_cloud, m_node_config.world_frame, m_package.cloud_end_time);
                }
            }

            // 发布非地面点云
            if (m_non_ground_cloud_pub->get_subscription_count() > 0) {
                auto non_ground_cloud = point_cloud_proc->getNonGroundCloud();
                if (non_ground_cloud && !non_ground_cloud->empty()) {
                    publishCloud(m_non_ground_cloud_pub, non_ground_cloud, m_node_config.world_frame, m_package.cloud_end_time);
                }
            }

            // 发布聚类结果（合并所有聚类为一个点云）
            if (m_cluster_clouds_pub->get_subscription_count() > 0) {
                auto cluster_clouds = point_cloud_proc->getClusterClouds();
                if (!cluster_clouds.empty()) {
                    CloudType::Ptr merged_clusters(new CloudType);
                    for (const auto& cluster : cluster_clouds) {
                        *merged_clusters += *cluster;
                    }
                    if (!merged_clusters->empty()) {
                        publishCloud(m_cluster_clouds_pub, merged_clusters, m_node_config.world_frame, m_package.cloud_end_time);
                    }
                }
            }
        }

        publishLaserScan(m_package.cloud, m_package.cloud_end_time);
    }

private:
    // 旋转四元数成员变量
    Eigen::Quaterniond m_odom_rotation;  // odom坐标系旋转(Z轴逆时针45度)
    Eigen::Quaterniond m_map_rotation;   // map坐标系旋转(Z轴顺时针135度)
    Eigen::Quaterniond m_lidar_flip;     // 雷达倒置旋转(X轴180度)
    Eigen::Matrix4d m_initial_pose_offset; // 初始位姿偏移矩阵

    rclcpp::Subscription<sensor_msgs::msg::Imu>::SharedPtr m_imu_sub;
    rclcpp::Subscription<livox_ros_driver2::msg::CustomMsg>::SharedPtr m_lidar_sub;

    rclcpp::Publisher<sensor_msgs::msg::PointCloud2>::SharedPtr m_body_cloud_pub;
    rclcpp::Publisher<sensor_msgs::msg::PointCloud2>::SharedPtr m_world_cloud_pub;
    rclcpp::Publisher<nav_msgs::msg::Path>::SharedPtr m_path_pub;
    rclcpp::Publisher<nav_msgs::msg::Odometry>::SharedPtr m_odom_pub;
    rclcpp::Publisher<sensor_msgs::msg::PointCloud2>::SharedPtr m_ground_cloud_pub;
    rclcpp::Publisher<sensor_msgs::msg::PointCloud2>::SharedPtr m_non_ground_cloud_pub;
    rclcpp::Publisher<sensor_msgs::msg::PointCloud2>::SharedPtr m_cluster_clouds_pub;
    rclcpp::Publisher<sensor_msgs::msg::LaserScan>::SharedPtr m_scan_pub;

    rclcpp::TimerBase::SharedPtr m_timer;
    StateData m_state_data;
    SyncPackage m_package;
    NodeConfig m_node_config;
    Config m_builder_config;
    std::shared_ptr<IESKF> m_kf;
    std::shared_ptr<MapBuilder> m_builder;
    std::shared_ptr<tf2_ros::TransformBroadcaster> m_tf_broadcaster;
};

int main(int argc, char **argv) {
    rclcpp::init(argc, argv);
    rclcpp::spin(std::make_shared<LIONode>());
    rclcpp::shutdown();
    return 0;
}
